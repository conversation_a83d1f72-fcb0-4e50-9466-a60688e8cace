'use client';

import { useAccount, useSwitch<PERSON>hain } from 'wagmi';
import { useState } from 'react';
import { AirdropABI } from '../utils/abis/AirdropABI';
import { base } from 'viem/chains';
import { parseEther } from 'viem';
import { useRouter } from 'next/navigation';
import {
  Transaction,
  TransactionButton,
  TransactionStatus,
  TransactionStatusLabel,
  TransactionStatusAction,
} from '@coinbase/onchainkit/transaction';
import {
  GAME_CONTRACT_ADDRESS,
  PLAY_FEE_ETH,
} from '../utils/constants';

interface PlayButtonProps {
  onSuccess: () => void;
  finalScore?: number;
}

export function PlayButton({ onSuccess, finalScore }: PlayButtonProps) {
  const router = useRouter();
  const account = useAccount();
  const [error, setError] = useState<string | null>(null);
  const { switchChain } = useSwitchChain();

  // Contract calls for the transaction
  const contracts = [
    {
      address: GAME_CONTRACT_ADDRESS as `0x${string}`,
      abi: AirdropABI,
      functionName: 'startGame',
      args: [true], // payWithETH = true
      value: parseEther(PLAY_FEE_ETH),
    },
  ];

  const handleSuccess = () => {
    console.log('Game started successfully!');
    onSuccess();
    router.push('/category-select');
  };

  const handleError = (error: any) => {
    console.error('Error starting game:', error);
    setError('Failed to start game. Please try again.');
  };

  const handleNetworkCheck = () => {
    if (!account.isConnected) {
      setError('Please connect your wallet first');
      return false;
    }

    if (!account.chainId) {
      setError('Please connect to Base network');
      return false;
    }

    if (account.chainId !== base.id) {
      switchChain({ chainId: base.id });
      return false;
    }

    setError(null);
    return true;
  };

  if (!account.isConnected) {
    return (
      <div className='w-full'>
        <button
          disabled
          className='w-full px-4 py-2 bg-gray-400 text-white rounded-lg cursor-not-allowed'
        >
          Connect Wallet First
        </button>
      </div>
    );
  }

  return (
    <div className='w-full'>
      {error && (
        <div className='mb-4'>
          <p className='text-red-500'>{error}</p>
        </div>
      )}

      <Transaction
        contracts={contracts}
        onSuccess={handleSuccess}
        onError={handleError}
        isSponsored={true}
      >
        <TransactionButton
          className='w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed'
          text={finalScore !== undefined ? 'Play Again' : 'Start Game'}
          onClick={handleNetworkCheck}
        />
        <TransactionStatus>
          <TransactionStatusLabel />
          <TransactionStatusAction />
        </TransactionStatus>
      </Transaction>
    </div>
  );
}
